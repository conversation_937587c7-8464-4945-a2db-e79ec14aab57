<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('auctions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('category_id')->constrained()->onDelete('restrict');
            $table->string('title', 500);
            $table->text('description');
            $table->string('condition', 50)->nullable();
            $table->decimal('starting_price', 12, 2);
            $table->decimal('reserve_price', 12, 2)->nullable();
            $table->decimal('current_bid', 12, 2)->default(0.00);
            $table->decimal('bid_increment', 12, 2)->default(1.00);
            $table->decimal('buyout_price', 12, 2)->nullable();
            $table->timestamp('start_time');
            $table->timestamp('end_time');
            $table->timestamp('actual_end_time')->nullable();
            $table->enum('status', ['draft', 'scheduled', 'active', 'ended', 'cancelled', 'suspended'])->default('draft');
            $table->boolean('auto_extend')->default(false);
            $table->integer('extend_minutes')->default(10);
            $table->decimal('shipping_cost', 10, 2)->nullable();
            $table->json('shipping_options')->nullable();
            $table->text('return_policy')->nullable();
            $table->text('terms_conditions')->nullable();
            $table->boolean('featured')->default(false);
            $table->timestamp('featured_until')->nullable();
            $table->integer('views_count')->default(0);
            $table->integer('watchers_count')->default(0);
            $table->integer('bids_count')->default(0);
            $table->foreignId('winner_id')->nullable()->constrained('users')->onDelete('set null');
            $table->decimal('final_price', 12, 2)->nullable();
            $table->decimal('commission_rate', 5, 4)->default(0.10);
            $table->decimal('commission_amount', 12, 2)->nullable();
            $table->string('payment_status', 50)->default('pending');
            $table->timestamp('payment_due_date')->nullable();
            $table->string('shipping_status', 50)->default('pending');
            $table->text('notes')->nullable();
            $table->text('admin_notes')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['status', 'end_time']);
            $table->index(['category_id', 'status']);
            $table->index(['user_id', 'status']);
            $table->index('featured');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('auctions');
    }
};
