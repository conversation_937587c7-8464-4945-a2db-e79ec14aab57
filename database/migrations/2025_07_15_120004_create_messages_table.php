<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sender_id')->constrained('users')->onDelete('restrict');
            $table->foreignId('receiver_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('support_ticket_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('auction_id')->nullable()->constrained()->onDelete('set null');
            $table->text('message');
            $table->timestamp('read_at')->nullable();
            $table->enum('type', ['private', 'support_reply', 'system_notification'])->default('private');
            $table->timestamps();

            // Indexes
            $table->index(['sender_id', 'receiver_id']);
            $table->index('support_ticket_id');
            $table->index('auction_id');
            $table->index(['receiver_id', 'read_at']);
            $table->index(['type', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
