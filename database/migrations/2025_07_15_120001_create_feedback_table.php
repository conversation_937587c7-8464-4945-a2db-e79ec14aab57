<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('feedback', function (Blueprint $table) {
            $table->id();
            $table->foreignId('giver_id')->constrained('users')->onDelete('restrict');
            $table->foreignId('receiver_id')->constrained('users')->onDelete('restrict');
            $table->foreignId('auction_id')->constrained()->onDelete('cascade');
            $table->tinyInteger('rating')->unsigned(); // 1-5 stars
            $table->text('comments')->nullable();
            $table->enum('feedback_type', ['buyer_to_seller', 'seller_to_buyer']);
            $table->timestamps();

            // Unique constraint to prevent duplicate feedback for same transaction
            $table->unique(['giver_id', 'receiver_id', 'auction_id'], 'uk_feedback_giver_receiver_auction');

            // Indexes
            $table->index('receiver_id');
            $table->index('auction_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('feedback');
    }
};
