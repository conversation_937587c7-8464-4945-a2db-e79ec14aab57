<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update users table - increase avatar column length
        Schema::table('users', function (Blueprint $table) {
            $table->string('avatar', 2048)->nullable()->change();
        });

        // Update auctions table
        Schema::table('auctions', function (Blueprint $table) {
            $table->longText('description')->change();
            $table->string('condition', 50)->nullable(false)->change();
            $table->date('payment_due_date')->nullable()->change();
        });

        // Update transactions table - add 'adjustment' to type enum
        Schema::table('transactions', function (Blueprint $table) {
            $table->enum('type', ['charge', 'refund', 'payout', 'fee', 'commission', 'adjustment'])->change();
        });

        // Update notifications table - change channels to JSON
        Schema::table('notifications', function (Blueprint $table) {
            $table->json('channels')->nullable()->change();
        });

        // Update settings table
        Schema::table('settings', function (Blueprint $table) {
            $table->longText('value')->nullable()->change();
            $table->enum('type', ['string', 'boolean', 'integer', 'float', 'json', 'text'])->default('string')->change();
            $table->boolean('is_public')->default(true)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert users table
        Schema::table('users', function (Blueprint $table) {
            $table->string('avatar')->nullable()->change();
        });

        // Revert auctions table
        Schema::table('auctions', function (Blueprint $table) {
            $table->text('description')->change();
            $table->string('condition', 50)->nullable()->change();
            $table->timestamp('payment_due_date')->nullable()->change();
        });

        // Revert transactions table
        Schema::table('transactions', function (Blueprint $table) {
            $table->enum('type', ['charge', 'refund', 'payout', 'fee', 'commission'])->change();
        });

        // Revert notifications table
        Schema::table('notifications', function (Blueprint $table) {
            $table->string('channels')->default('database')->change();
        });

        // Revert settings table
        Schema::table('settings', function (Blueprint $table) {
            $table->text('value')->nullable()->change();
            $table->string('type')->default('string')->change();
            $table->boolean('is_public')->default(false)->change();
        });
    }
};
