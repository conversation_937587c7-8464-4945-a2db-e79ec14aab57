<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bids', function (Blueprint $table) {
            $table->id();
            $table->foreignId('auction_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->decimal('amount', 12, 2);
            $table->decimal('max_bid', 12, 2)->nullable(); // for proxy bidding
            $table->enum('bid_type', ['manual', 'proxy', 'auto'])->default('manual');
            $table->timestamp('timestamp')->useCurrent();
            $table->boolean('is_winning')->default(false);
            $table->boolean('is_valid')->default(true);
            $table->timestamp('invalidated_at')->nullable();
            $table->text('invalidation_reason')->nullable();
            $table->ipAddress('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['auction_id', 'timestamp']);
            $table->index(['auction_id', 'is_winning']);
            $table->index(['user_id', 'timestamp']);
            $table->index(['auction_id', 'amount']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bids');
    }
};
