<?php

namespace Database\Factories;

use App\Models\Auction;
use App\Models\Feedback;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Feedback>
 */
class FeedbackFactory extends Factory
{
    protected $model = Feedback::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'giver_id' => User::factory(),
            'receiver_id' => User::factory(),
            'auction_id' => Auction::factory(),
            'rating' => $this->faker->numberBetween(1, 5),
            'comments' => $this->faker->optional(0.8)->paragraph(),
            'feedback_type' => $this->faker->randomElement(['buyer_to_seller', 'seller_to_buyer']),
        ];
    }

    /**
     * Indicate that the feedback is from buyer to seller.
     */
    public function buyerToSeller(): static
    {
        return $this->state(fn (array $attributes) => [
            'feedback_type' => 'buyer_to_seller',
        ]);
    }

    /**
     * Indicate that the feedback is from seller to buyer.
     */
    public function sellerToBuyer(): static
    {
        return $this->state(fn (array $attributes) => [
            'feedback_type' => 'seller_to_buyer',
        ]);
    }

    /**
     * Indicate that the feedback has a high rating.
     */
    public function positive(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $this->faker->numberBetween(4, 5),
            'comments' => $this->faker->randomElement([
                'Excellent transaction! Highly recommended.',
                'Great communication and fast shipping.',
                'Item exactly as described. Very satisfied.',
                'Professional and reliable. Would buy again.',
            ]),
        ]);
    }

    /**
     * Indicate that the feedback has a low rating.
     */
    public function negative(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $this->faker->numberBetween(1, 2),
            'comments' => $this->faker->randomElement([
                'Item not as described.',
                'Poor communication and slow shipping.',
                'Had issues with the transaction.',
                'Would not recommend.',
            ]),
        ]);
    }
}
