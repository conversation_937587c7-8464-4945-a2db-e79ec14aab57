<?php

namespace Database\Factories;

use App\Models\Auction;
use App\Models\Message;
use App\Models\SupportTicket;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Message>
 */
class MessageFactory extends Factory
{
    protected $model = Message::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $types = ['private', 'support_reply', 'system_notification'];

        return [
            'sender_id' => User::factory(),
            'receiver_id' => User::factory(),
            'support_ticket_id' => null,
            'auction_id' => null,
            'message' => $this->faker->paragraphs(2, true),
            'read_at' => $this->faker->optional(0.6)->dateTimeBetween('-1 week', 'now'),
            'type' => $this->faker->randomElement($types),
        ];
    }

    /**
     * Indicate that the message is private.
     */
    public function private(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'private',
            'support_ticket_id' => null,
        ]);
    }

    /**
     * Indicate that the message is a support reply.
     */
    public function supportReply(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'support_reply',
            'support_ticket_id' => SupportTicket::factory(),
            'receiver_id' => null,
        ]);
    }

    /**
     * Indicate that the message is a system notification.
     */
    public function systemNotification(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'system_notification',
            'receiver_id' => User::factory(),
            'message' => $this->faker->randomElement([
                'Your auction has ended.',
                'You have been outbid.',
                'Payment received successfully.',
                'Your item has been shipped.',
            ]),
        ]);
    }

    /**
     * Indicate that the message is unread.
     */
    public function unread(): static
    {
        return $this->state(fn (array $attributes) => [
            'read_at' => null,
        ]);
    }

    /**
     * Indicate that the message is read.
     */
    public function read(): static
    {
        return $this->state(fn (array $attributes) => [
            'read_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Associate the message with an auction.
     */
    public function forAuction(Auction $auction = null): static
    {
        return $this->state(fn (array $attributes) => [
            'auction_id' => $auction?->id ?? Auction::factory(),
        ]);
    }

    /**
     * Associate the message with a support ticket.
     */
    public function forSupportTicket(SupportTicket $ticket = null): static
    {
        return $this->state(fn (array $attributes) => [
            'support_ticket_id' => $ticket?->id ?? SupportTicket::factory(),
            'type' => 'support_reply',
        ]);
    }
}
