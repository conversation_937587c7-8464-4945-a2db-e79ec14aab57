<?php

namespace Database\Factories;

use App\Models\Faq;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Faq>
 */
class FaqFactory extends Factory
{
    protected $model = Faq::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = ['Bidding', 'Payments', 'Account', 'Shipping', 'General'];
        
        return [
            'question' => $this->faker->sentence() . '?',
            'answer' => $this->faker->paragraphs(2, true),
            'category' => $this->faker->randomElement($categories),
            'sort_order' => $this->faker->numberBetween(0, 100),
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
        ];
    }

    /**
     * Indicate that the FAQ is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the FAQ is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Set the FAQ category.
     */
    public function category(string $category): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => $category,
        ]);
    }

    /**
     * Create FAQ about bidding.
     */
    public function bidding(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'Bidding',
            'question' => $this->faker->randomElement([
                'How do I place a bid?',
                'What is proxy bidding?',
                'Can I retract my bid?',
                'When does bidding end?',
            ]),
        ]);
    }

    /**
     * Create FAQ about payments.
     */
    public function payments(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'Payments',
            'question' => $this->faker->randomElement([
                'What payment methods are accepted?',
                'When do I need to pay?',
                'Are there any fees?',
                'How do refunds work?',
            ]),
        ]);
    }
}
