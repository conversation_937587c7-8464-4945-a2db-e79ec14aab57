<?php

namespace Database\Seeders;

use App\Models\Faq;
use Illuminate\Database\Seeder;

class FaqSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faqs = [
            // Bidding FAQs
            [
                'question' => 'How do I place a bid?',
                'answer' => 'To place a bid, simply enter your bid amount in the bid box on the auction page and click "Place Bid". Your bid must be higher than the current bid plus the minimum increment.',
                'category' => 'Bidding',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'question' => 'What is proxy bidding?',
                'answer' => 'Proxy bidding allows you to set a maximum bid amount. The system will automatically bid on your behalf up to your maximum amount, only bidding as much as necessary to maintain your lead.',
                'category' => 'Bidding',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'question' => 'Can I retract my bid?',
                'answer' => 'Bids are generally binding and cannot be retracted. However, in exceptional circumstances, you may contact support within 1 hour of placing the bid.',
                'category' => 'Bidding',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'question' => 'When does bidding end?',
                'answer' => 'Bidding ends at the scheduled end time. Some auctions may have auto-extend features that add extra time if bids are placed in the final minutes.',
                'category' => 'Bidding',
                'sort_order' => 4,
                'is_active' => true,
            ],

            // Payment FAQs
            [
                'question' => 'What payment methods are accepted?',
                'answer' => 'We accept major credit cards (Visa, MasterCard, American Express), PayPal, and bank transfers for larger purchases.',
                'category' => 'Payments',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'question' => 'When do I need to pay?',
                'answer' => 'Payment is due within 3 days of winning an auction. You will receive an email with payment instructions immediately after the auction ends.',
                'category' => 'Payments',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'question' => 'Are there any fees?',
                'answer' => 'Buyers pay a 3% processing fee on the final sale price. This fee covers payment processing and platform maintenance.',
                'category' => 'Payments',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'question' => 'How do refunds work?',
                'answer' => 'Refunds are processed according to our return policy. If eligible, refunds are issued to the original payment method within 5-10 business days.',
                'category' => 'Payments',
                'sort_order' => 4,
                'is_active' => true,
            ],

            // Account FAQs
            [
                'question' => 'How do I create an account?',
                'answer' => 'Click "Sign Up" in the top right corner, fill in your details, and verify your email address. You can start bidding immediately after verification.',
                'category' => 'Account',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'question' => 'How do I verify my account?',
                'answer' => 'Account verification involves confirming your email address and optionally providing additional identity verification for higher bidding limits.',
                'category' => 'Account',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'question' => 'Can I change my username?',
                'answer' => 'Usernames cannot be changed after account creation. If you need to change your username, please contact support.',
                'category' => 'Account',
                'sort_order' => 3,
                'is_active' => true,
            ],

            // Shipping FAQs
            [
                'question' => 'How much does shipping cost?',
                'answer' => 'Shipping costs vary by item and location. The shipping cost is displayed on each auction listing and is paid separately from the winning bid.',
                'category' => 'Shipping',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'question' => 'How long does shipping take?',
                'answer' => 'Shipping times depend on the seller and shipping method chosen. Most items ship within 1-3 business days after payment is received.',
                'category' => 'Shipping',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'question' => 'Can I track my shipment?',
                'answer' => 'Yes, you will receive tracking information via email once your item ships. You can also view tracking details in your account dashboard.',
                'category' => 'Shipping',
                'sort_order' => 3,
                'is_active' => true,
            ],

            // General FAQs
            [
                'question' => 'How do I contact customer support?',
                'answer' => 'You can contact support through the "Contact Us" page, by <NAME_EMAIL>, or by creating a support ticket in your account dashboard.',
                'category' => 'General',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'question' => 'Is my personal information secure?',
                'answer' => 'Yes, we use industry-standard encryption and security measures to protect your personal and payment information. We never share your data with third parties without your consent.',
                'category' => 'General',
                'sort_order' => 2,
                'is_active' => true,
            ],
        ];

        foreach ($faqs as $faq) {
            Faq::create($faq);
        }
    }
}
