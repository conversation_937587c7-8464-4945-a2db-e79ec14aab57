import React, { useState } from 'react';
import { useForm } from '@inertiajs/react';
import { <PERSON><PERSON>l, DollarSign, TrendingUp } from 'lucide-react';
import { Auction, PlaceBidForm } from '../../types';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Checkbox } from '../ui/checkbox';
import { Alert, AlertDescription } from '../ui/alert';
import { cn } from '../../lib/utils';

interface BidFormProps {
  auction: Auction;
  className?: string;
}

const BidForm: React.FC<BidFormProps> = ({ auction, className }) => {
  const [isProxyBid, setIsProxyBid] = useState(false);

  const { data, setData, post, processing, errors, reset } = useForm<PlaceBidForm>({
    auction_id: auction.id,
    amount: 0,
    max_bid: undefined,
    currency: auction.current_bid.currency,
    bid_type: 'manual',
  });

  const minimumBid = auction.current_bid.amount > 0
    ? auction.current_bid.amount + 1 // Minimum increment
    : auction.starting_price.amount;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const submitData = {
      ...data,
      bid_type: isProxyBid ? 'proxy' : 'manual',
    };

    post('/bids', {
      data: submitData,
      onSuccess: () => {
        reset();
        setIsProxyBid(false);
      },
    });
  };

  const handleAmountChange = (value: string) => {
    const numValue = parseFloat(value) || 0;
    setData('amount', numValue);
  };

  const handleMaxBidChange = (value: string) => {
    const numValue = parseFloat(value) || 0;
    setData('max_bid', numValue);
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: data.currency,
    }).format(amount);
  };

  const canBid = auction.metadata?.can_bid && auction.status === 'active';

  if (!canBid) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert>
            <AlertDescription>
              {auction.status !== 'active'
                ? 'This auction is not currently active for bidding.'
                : 'You cannot bid on this auction.'
              }
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Gavel className="h-5 w-5 mr-2" />
          Place Your Bid
        </CardTitle>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Current Bid Info */}
          <div className="bg-muted p-4 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-muted-foreground">Current Bid</span>
              <span className="font-bold text-lg">{auction.current_bid.formatted}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Minimum Bid</span>
              <span className="font-semibold text-primary">{formatCurrency(minimumBid)}</span>
            </div>
          </div>

          {/* Bid Amount */}
          <div className="space-y-2">
            <Label htmlFor="amount">Your Bid Amount</Label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="amount"
                type="number"
                step="0.01"
                min={minimumBid}
                value={data.amount || ''}
                onChange={(e) => handleAmountChange(e.target.value)}
                className="pl-10"
                placeholder={`Minimum ${formatCurrency(minimumBid)}`}
                required
              />
            </div>
            {errors.amount && (
              <p className="text-sm text-red-600">{errors.amount}</p>
            )}
          </div>

          {/* Proxy Bidding */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="proxy-bid"
                checked={isProxyBid}
                onCheckedChange={setIsProxyBid}
              />
              <Label htmlFor="proxy-bid" className="text-sm">
                Enable Proxy Bidding
              </Label>
            </div>

            {isProxyBid && (
              <div className="space-y-2 pl-6">
                <Label htmlFor="max_bid" className="text-sm">
                  Maximum Bid Amount
                </Label>
                <div className="relative">
                  <TrendingUp className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="max_bid"
                    type="number"
                    step="0.01"
                    min={data.amount}
                    value={data.max_bid || ''}
                    onChange={(e) => handleMaxBidChange(e.target.value)}
                    className="pl-10"
                    placeholder="Enter maximum amount"
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  We'll automatically bid up to this amount to keep you in the lead.
                </p>
                {errors.max_bid && (
                  <p className="text-sm text-red-600">{errors.max_bid}</p>
                )}
              </div>
            )}
          </div>

          {/* Quick Bid Buttons */}
          <div className="space-y-2">
            <Label className="text-sm">Quick Bid</Label>
            <div className="grid grid-cols-3 gap-2">
              {[minimumBid, minimumBid + 5, minimumBid + 10].map((amount) => (
                <Button
                  key={amount}
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setData('amount', amount)}
                  className="text-xs"
                >
                  {formatCurrency(amount)}
                </Button>
              ))}
            </div>
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={processing || data.amount < minimumBid}
            className="w-full"
            size="lg"
          >
            {processing ? (
              'Placing Bid...'
            ) : (
              <>
                <Gavel className="h-4 w-4 mr-2" />
                Place Bid {data.amount > 0 && `- ${formatCurrency(data.amount)}`}
              </>
            )}
          </Button>

          {/* Error Display */}
          {errors.error && (
            <Alert variant="destructive">
              <AlertDescription>{errors.error}</AlertDescription>
            </Alert>
          )}

          {/* Bidding Info */}
          <div className="text-xs text-muted-foreground space-y-1">
            <p>• By placing a bid, you agree to purchase this item if you win.</p>
            <p>• Bids are binding and cannot be retracted.</p>
            {auction.features.auto_extend && (
              <p>• This auction may be extended if bids are placed near the end time.</p>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default BidForm;
