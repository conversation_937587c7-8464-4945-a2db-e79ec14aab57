import React, { useState } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import {
  Heart,
  Eye,
  Clock,
  Gavel,
  User,
  MapPin,
  Truck,
  Shield,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { Auction, PageProps } from '../../types';
import AppLayout from '../../layouts/app-layout';
import BidForm from '../../components/bid/BidForm';
import BidHistory from '../../components/bid/BidHistory';
import CountdownTimer from '../../components/auction/CountdownTimer';
import AuctionCard from '../../components/auction/AuctionCard';
import { Button } from '../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { cn } from '../../lib/utils';

interface Props extends PageProps {
  auction: Auction;
  relatedAuctions: Auction[];
}

const AuctionShow: React.FC<Props> = ({ auction, relatedAuctions }) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isWatched, setIsWatched] = useState(auction.metadata?.is_watched || false);

  const images = auction.images || [];
  const primaryImage = images[currentImageIndex] || images[0];

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600';
      case 'ended':
        return 'text-red-600';
      case 'scheduled':
        return 'text-yellow-600';
      default:
        return 'text-muted-foreground';
    }
  };

  return (
    <AppLayout>
      <Head title={auction.title} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="mb-6">
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Link href="/auctions" className="hover:text-primary">Auctions</Link>
            <span>/</span>
            <Link href={`/categories/${auction.category?.id}`} className="hover:text-primary">
              {auction.category?.name}
            </Link>
            <span>/</span>
            <span className="text-foreground">{auction.title}</span>
          </div>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Images and Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Image Gallery */}
            <Card>
              <CardContent className="p-0">
                <div className="relative aspect-[4/3] bg-muted">
                  {primaryImage ? (
                    <img
                      src={primaryImage.url}
                      alt={primaryImage.alt_text || auction.title}
                      className="w-full h-full object-cover rounded-t-lg"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <Gavel className="h-16 w-16 text-muted-foreground" />
                    </div>
                  )}

                  {/* Image Navigation */}
                  {images.length > 1 && (
                    <>
                      <Button
                        variant="secondary"
                        size="icon"
                        className="absolute left-4 top-1/2 transform -translate-y-1/2"
                        onClick={prevImage}
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="secondary"
                        size="icon"
                        className="absolute right-4 top-1/2 transform -translate-y-1/2"
                        onClick={nextImage}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </>
                  )}

                  {/* Image Counter */}
                  {images.length > 1 && (
                    <div className="absolute bottom-4 right-4 bg-black/50 text-white px-2 py-1 rounded text-sm">
                      {currentImageIndex + 1} / {images.length}
                    </div>
                  )}
                </div>

                {/* Thumbnail Strip */}
                {images.length > 1 && (
                  <div className="p-4 flex space-x-2 overflow-x-auto">
                    {images.map((image, index) => (
                      <button
                        key={image.id}
                        onClick={() => setCurrentImageIndex(index)}
                        className={cn(
                          'flex-shrink-0 w-16 h-16 rounded border-2 overflow-hidden',
                          index === currentImageIndex ? 'border-primary' : 'border-muted'
                        )}
                      >
                        <img
                          src={image.thumbnail_url || image.url}
                          alt={`${auction.title} ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Auction Details */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-2xl mb-2">{auction.title}</CardTitle>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <Badge variant="outline" className={getStatusColor(auction.status)}>
                        {auction.status ? auction.status.charAt(0).toUpperCase() + auction.status.slice(1) : 'Unknown'}
                      </Badge>
                      {auction.condition && (
                        <span>Condition: {auction.condition.replace('_', ' ')}</span>
                      )}
                      <div className="flex items-center">
                        <Eye className="h-4 w-4 mr-1" />
                        {auction.statistics?.views_count || 0} views
                      </div>
                    </div>
                  </div>

                  {auction.features?.is_featured && (
                    <Badge variant="default">Featured</Badge>
                  )}
                </div>
              </CardHeader>

              <CardContent>
                <Tabs defaultValue="description" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="description">Description</TabsTrigger>
                    <TabsTrigger value="shipping">Shipping</TabsTrigger>
                    <TabsTrigger value="seller">Seller</TabsTrigger>
                  </TabsList>

                  <TabsContent value="description" className="mt-4">
                    <div className="prose max-w-none">
                      <p className="whitespace-pre-wrap">{auction.description}</p>
                    </div>
                  </TabsContent>

                  <TabsContent value="shipping" className="mt-4">
                    <div className="space-y-3">
                      {auction.shipping_cost && (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <Truck className="h-4 w-4 mr-2" />
                            <span>Shipping Cost</span>
                          </div>
                          <span className="font-medium">{auction.shipping_cost?.formatted}</span>
                        </div>
                      )}
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-2" />
                        <span>Ships from: {auction.seller.profile?.location || 'Not specified'}</span>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="seller" className="mt-4">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                        <User className="h-6 w-6" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold">{auction.seller.name}</h4>
                        <p className="text-sm text-muted-foreground mb-2">
                          Member since {new Date(auction.seller.metadata?.created_at || auction.seller.created_at).getFullYear()}
                        </p>
                        {auction.seller.statistics && (
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-muted-foreground">Total Auctions:</span>
                              <span className="ml-2 font-medium">{auction.seller.statistics?.total_auctions || 0}</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Success Rate:</span>
                              <span className="ml-2 font-medium">{auction.seller.statistics?.success_rate || 0}%</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            {/* Bid History */}
            <BidHistory auctionId={auction.id} />
          </div>

          {/* Right Column - Bidding */}
          <div className="space-y-6">
            {/* Current Bid Info */}
            <Card>
              <CardContent className="p-6">
                <div className="text-center mb-4">
                  <div className="text-sm text-muted-foreground mb-1">Current Bid</div>
                  <div className="text-3xl font-bold text-primary mb-2">
                    {auction.current_bid?.formatted || '$0.00'}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {auction.statistics?.bids_count || 0} bid{(auction.statistics?.bids_count || 0) !== 1 ? 's' : ''}
                  </div>
                </div>

                {auction.status === 'active' && (
                  <div className="text-center mb-4">
                    <div className="text-sm text-muted-foreground mb-2">Time Remaining</div>
                    <CountdownTimer
                      endTime={auction.timing?.end_time}
                      size="lg"
                      className="justify-center"
                    />
                  </div>
                )}

                {auction.reserve_price && (
                  <div className="flex items-center justify-between text-sm mb-4 p-3 bg-muted rounded">
                    <span>Reserve Price</span>
                    <span className={cn(
                      'font-medium',
                      auction.reserve_price?.is_met ? 'text-green-600' : 'text-yellow-600'
                    )}>
                      {auction.reserve_price?.is_met ? 'Met' : 'Not Met'}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Bid Form */}
            <BidForm auction={auction} />

            {/* Watch Button */}
            <Button
              variant="outline"
              className="w-full"
              onClick={() => {
                // Handle watch toggle
                setIsWatched(!isWatched);
              }}
            >
              <Heart className={cn('h-4 w-4 mr-2', isWatched && 'fill-current')} />
              {isWatched ? 'Remove from Watchlist' : 'Add to Watchlist'}
            </Button>
          </div>
        </div>

        {/* Related Auctions */}
        {relatedAuctions.length > 0 && (
          <div className="mt-12">
            <h2 className="text-2xl font-bold mb-6">Related Auctions</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedAuctions.map((relatedAuction) => (
                <AuctionCard key={relatedAuction.id} auction={relatedAuction} />
              ))}
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default AuctionShow;
