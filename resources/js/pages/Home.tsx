import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { <PERSON>R<PERSON>, Gavel, Shield, Clock, TrendingUp } from 'lucide-react';
import AppLayout from '@/Layouts/AppLayout';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { PageProps, Auction } from '@/types';

interface HomeProps extends PageProps {
  featuredAuctions: Auction[];
  endingSoonAuctions: Auction[];
}

const Home: React.FC<HomeProps> = ({ featuredAuctions, endingSoonAuctions }) => {
  const features = [
    {
      icon: Gavel,
      title: 'Live Bidding',
      description: 'Participate in real-time auctions with instant bid updates and notifications.',
    },
    {
      icon: Shield,
      title: 'Secure Payments',
      description: 'Safe and secure payment processing with buyer and seller protection.',
    },
    {
      icon: Clock,
      title: '24/7 Auctions',
      description: 'Auctions running around the clock with items from around the world.',
    },
    {
      icon: TrendingUp,
      title: 'Best Prices',
      description: 'Competitive bidding ensures you get the best deals on unique items.',
    },
  ];

  return (
    <AppLayout>
      <Head title="AuctionHub - Premier Online Auction Platform" />

      <div className="space-y-16">
        {/* Hero Section */}
        <section className="relative bg-gradient-to-r from-blue-600 to-blue-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div className="text-center">
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Discover Amazing
                <span className="block text-blue-200">Auction Deals</span>
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
                Join thousands of buyers and sellers in the world's most exciting online auction platform.
                Find unique items, rare collectibles, and incredible deals.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auctions">
                  <Button size="lg" variant="secondary" className="w-full sm:w-auto">
                    Browse Auctions
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link href="/register">
                  <Button size="lg" variant="outline" className="w-full sm:w-auto border-white text-white hover:bg-white hover:text-blue-600">
                    Start Selling
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose AuctionHub?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              We provide the best auction experience with cutting-edge technology and unmatched security.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="mx-auto w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Featured Auctions */}
        <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">Featured Auctions</h2>
              <p className="text-gray-600">Hand-picked premium auctions you don't want to miss</p>
            </div>
            <Link href="/auctions?featured=true">
              <Button variant="outline">
                View All
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>

          {featuredAuctions && featuredAuctions.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {featuredAuctions.slice(0, 4).map((auction) => (
                <Card key={auction.id} className="group hover:shadow-lg transition-all duration-200">
                  <Link href={`/auctions/${auction.id}`}>
                    <div className="relative aspect-[4/3] overflow-hidden rounded-t-lg">
                      {auction.images && auction.images.length > 0 ? (
                        <img
                          src={auction.images[0].thumbnail_url || auction.images[0].url}
                          alt={auction.images[0].alt_text || auction.title}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                          <Gavel className="h-12 w-12 text-gray-400" />
                        </div>
                      )}

                      <div className="absolute top-3 left-3">
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {auction.status.charAt(0).toUpperCase() + auction.status.slice(1)}
                        </Badge>
                      </div>

                      {auction.features.is_featured && (
                        <div className="absolute top-3 right-3">
                          <Badge className="bg-blue-100 text-blue-800">
                            Featured
                          </Badge>
                        </div>
                      )}
                    </div>

                    <CardContent className="p-4">
                      <h3 className="font-semibold text-lg text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
                        {auction.title}
                      </h3>

                      <div className="space-y-2 mb-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Current Bid</span>
                          <span className="font-bold text-lg text-blue-600">
                            {auction.current_bid.formatted}
                          </span>
                        </div>

                        {auction.timing.remaining_minutes !== null && auction.timing.remaining_minutes > 0 && (
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">Time Left</span>
                            <span className={`font-medium ${auction.timing.is_ending_soon ? 'text-red-600' : 'text-gray-900'}`}>
                              {Math.floor(auction.timing.remaining_minutes / 60)}h {auction.timing.remaining_minutes % 60}m
                            </span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center justify-between text-sm text-gray-600 pt-3 border-t border-gray-100">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center">
                            <Gavel className="h-4 w-4 mr-1" />
                            <span>{auction.statistics?.bids_count || 0}</span>
                          </div>
                        </div>

                        {auction.seller && (
                          <div className="text-right">
                            <div className="font-medium text-gray-900">
                              {auction.seller.name}
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Link>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Gavel className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-600">No featured auctions available at the moment.</p>
            </div>
          )}
        </section>

        {/* Ending Soon */}
        <section className="bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div className="flex items-center justify-between mb-8">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-2">Ending Soon</h2>
                <p className="text-gray-600">Don't miss out on these auctions ending within 24 hours</p>
              </div>
              <Link href="/auctions?ending_soon=true">
                <Button variant="outline">
                  View All
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>

            {endingSoonAuctions && endingSoonAuctions.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {endingSoonAuctions.slice(0, 4).map((auction) => (
                  <Card key={auction.id} className="group hover:shadow-lg transition-all duration-200">
                    <Link href={`/auctions/${auction.id}`}>
                      <div className="relative aspect-[4/3] overflow-hidden rounded-t-lg">
                        {auction.images && auction.images.length > 0 ? (
                          <img
                            src={auction.images[0].thumbnail_url || auction.images[0].url}
                            alt={auction.images[0].alt_text || auction.title}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                            <Gavel className="h-12 w-12 text-gray-400" />
                          </div>
                        )}

                        <div className="absolute bottom-3 left-3">
                          <Badge variant="destructive" className="bg-red-100 text-red-800">
                            <Clock className="h-3 w-3 mr-1" />
                            Ending Soon
                          </Badge>
                        </div>
                      </div>

                      <CardContent className="p-4">
                        <h3 className="font-semibold text-lg text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
                          {auction.title}
                        </h3>

                        <div className="space-y-2 mb-3">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">Current Bid</span>
                            <span className="font-bold text-lg text-blue-600">
                              {auction.current_bid.formatted}
                            </span>
                          </div>

                          {auction.timing.remaining_minutes !== null && (
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-gray-600">Time Left</span>
                              <span className="font-medium text-red-600">
                                {auction.timing.remaining_minutes > 0
                                  ? `${Math.floor(auction.timing.remaining_minutes / 60)}h ${auction.timing.remaining_minutes % 60}m`
                                  : 'Ended'
                                }
                              </span>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Link>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Clock className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p className="text-gray-600">No auctions ending soon.</p>
              </div>
            )}
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-blue-600">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div className="text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Ready to Start Bidding?
              </h2>
              <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                Join our community of buyers and sellers. Create your account today and start participating in exciting auctions.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/register">
                  <Button size="lg" variant="secondary" className="w-full sm:w-auto">
                    Sign Up Now
                  </Button>
                </Link>
                <Link href="/auctions">
                  <Button size="lg" variant="outline" className="w-full sm:w-auto border-white text-white hover:bg-white hover:text-blue-600">
                    Browse Auctions
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
      </div>
    </AppLayout>
  );
};

export default Home;
