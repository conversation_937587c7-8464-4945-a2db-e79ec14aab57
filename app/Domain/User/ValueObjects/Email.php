<?php

declare(strict_types=1);

namespace App\Domain\User\ValueObjects;

use App\Domain\Shared\ValueObjects\ValueObject;
use InvalidArgumentException;

class Email extends ValueObject
{
    private string $value;

    public function __construct(string $value)
    {
        $this->validate($value);
        $this->value = strtolower(trim($value));
    }

    public static function fromString(string $value): self
    {
        return new self($value);
    }

    public function value(): string
    {
        return $this->value;
    }

    public function getDomain(): string
    {
        return substr($this->value, strpos($this->value, '@') + 1);
    }

    public function getLocalPart(): string
    {
        return substr($this->value, 0, strpos($this->value, '@'));
    }

    public function isGmail(): bool
    {
        return $this->getDomain() === 'gmail.com';
    }

    public function isYahoo(): bool
    {
        return in_array($this->getDomain(), ['yahoo.com', 'yahoo.co.uk', 'yahoo.ca']);
    }

    public function isOutlook(): bool
    {
        return in_array($this->getDomain(), ['outlook.com', 'hotmail.com', 'live.com']);
    }

    public function isDisposable(): bool
    {
        // List of common disposable email domains
        $disposableDomains = [
            '10minutemail.com',
            'tempmail.org',
            'guerrillamail.com',
            'mailinator.com',
            'throwaway.email',
        ];

        return in_array($this->getDomain(), $disposableDomains);
    }

    private function validate(string $value): void
    {
        if (empty(trim($value))) {
            throw new InvalidArgumentException('Email cannot be empty');
        }

        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
            throw new InvalidArgumentException('Invalid email format');
        }

        if (strlen($value) > 254) {
            throw new InvalidArgumentException('Email cannot be longer than 254 characters');
        }

        // Check for common security issues
        if (strpos($value, '..') !== false) {
            throw new InvalidArgumentException('Email cannot contain consecutive dots');
        }
    }
}
