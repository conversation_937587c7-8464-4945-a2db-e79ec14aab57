<?php

declare(strict_types=1);

namespace App\Infrastructure\Services;

use App\Domain\User\ValueObjects\UserId;
use App\Models\Notification;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * Send a notification to a user
     */
    public function send(
        UserId $userId,
        string $type,
        string $title,
        string $message,
        array $data = [],
        array $channels = ['database'],
        string $priority = 'normal',
        ?\DateTime $expiresAt = null,
        ?string $actionUrl = null,
        ?string $actionText = null
    ): Notification {
        $notification = Notification::create([
            'user_id' => $userId->value(),
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => $data,
            'channels' => $channels,
            'priority' => $priority,
            'expires_at' => $expiresAt,
            'action_url' => $actionUrl,
            'action_text' => $actionText,
        ]);

        // Send through specified channels
        foreach ($channels as $channel) {
            $this->sendThroughChannel($notification, $channel);
        }

        return $notification;
    }

    /**
     * Send bid notification
     */
    public function sendBidNotification(
        UserId $bidderId,
        UserId $sellerId,
        int $auctionId,
        string $auctionTitle,
        float $bidAmount
    ): void {
        // Notify seller about new bid
        $this->send(
            $sellerId,
            'bid_placed',
            'New Bid on Your Auction',
            "Someone placed a bid of $" . number_format($bidAmount, 2) . " on your auction: {$auctionTitle}",
            [
                'auction_id' => $auctionId,
                'bid_amount' => $bidAmount,
                'bidder_id' => $bidderId->value(),
            ],
            ['database', 'email'],
            'normal',
            null,
            route('auctions.show', $auctionId),
            'View Auction'
        );
    }

    /**
     * Send outbid notification
     */
    public function sendOutbidNotification(
        UserId $outbidUserId,
        int $auctionId,
        string $auctionTitle,
        float $newBidAmount
    ): void {
        $this->send(
            $outbidUserId,
            'outbid',
            'You\'ve Been Outbid',
            "You've been outbid on \"{$auctionTitle}\". The new highest bid is $" . number_format($newBidAmount, 2),
            [
                'auction_id' => $auctionId,
                'new_bid_amount' => $newBidAmount,
            ],
            ['database', 'email'],
            'high',
            null,
            route('auctions.show', $auctionId),
            'Place New Bid'
        );
    }

    /**
     * Send auction ending notification
     */
    public function sendAuctionEndingNotification(
        UserId $userId,
        int $auctionId,
        string $auctionTitle,
        int $hoursRemaining
    ): void {
        $this->send(
            $userId,
            'auction_ending',
            'Auction Ending Soon',
            "The auction \"{$auctionTitle}\" is ending in {$hoursRemaining} hours.",
            [
                'auction_id' => $auctionId,
                'hours_remaining' => $hoursRemaining,
            ],
            ['database', 'email'],
            'high',
            null,
            route('auctions.show', $auctionId),
            'View Auction'
        );
    }

    /**
     * Send auction won notification
     */
    public function sendAuctionWonNotification(
        UserId $winnerId,
        int $auctionId,
        string $auctionTitle,
        float $finalPrice
    ): void {
        $this->send(
            $winnerId,
            'auction_won',
            'Congratulations! You Won an Auction',
            "You won the auction \"{$auctionTitle}\" with a bid of $" . number_format($finalPrice, 2),
            [
                'auction_id' => $auctionId,
                'final_price' => $finalPrice,
            ],
            ['database', 'email'],
            'high',
            null,
            route('payments.checkout', $auctionId),
            'Complete Payment'
        );
    }

    /**
     * Send auction ended notification to seller
     */
    public function sendAuctionEndedNotification(
        UserId $sellerId,
        int $auctionId,
        string $auctionTitle,
        ?UserId $winnerId = null,
        ?float $finalPrice = null
    ): void {
        if ($winnerId && $finalPrice) {
            $message = "Your auction \"{$auctionTitle}\" has ended with a winning bid of $" . number_format($finalPrice, 2);
            $actionText = 'View Sale Details';
        } else {
            $message = "Your auction \"{$auctionTitle}\" has ended without any bids.";
            $actionText = 'View Auction';
        }

        $this->send(
            $sellerId,
            'auction_ended',
            'Your Auction Has Ended',
            $message,
            [
                'auction_id' => $auctionId,
                'winner_id' => $winnerId?->value(),
                'final_price' => $finalPrice,
            ],
            ['database', 'email'],
            'normal',
            null,
            route('auctions.show', $auctionId),
            $actionText
        );
    }

    /**
     * Send payment confirmation notification
     */
    public function sendPaymentConfirmationNotification(
        UserId $userId,
        int $paymentId,
        float $amount,
        string $description
    ): void {
        $this->send(
            $userId,
            'payment_confirmed',
            'Payment Confirmed',
            "Your payment of $" . number_format($amount, 2) . " for {$description} has been confirmed.",
            [
                'payment_id' => $paymentId,
                'amount' => $amount,
            ],
            ['database', 'email'],
            'normal'
        );
    }

    /**
     * Send bulk notifications
     */
    public function sendBulk(array $notifications): void
    {
        foreach ($notifications as $notification) {
            $this->send(
                $notification['user_id'],
                $notification['type'],
                $notification['title'],
                $notification['message'],
                $notification['data'] ?? [],
                $notification['channels'] ?? ['database'],
                $notification['priority'] ?? 'normal',
                $notification['expires_at'] ?? null,
                $notification['action_url'] ?? null,
                $notification['action_text'] ?? null
            );
        }
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(int $notificationId): void
    {
        Notification::where('id', $notificationId)->update(['read_at' => now()]);
    }

    /**
     * Mark all user notifications as read
     */
    public function markAllAsRead(UserId $userId): void
    {
        Notification::where('user_id', $userId->value())
            ->whereNull('read_at')
            ->update(['read_at' => now()]);
    }

    /**
     * Get unread notifications count
     */
    public function getUnreadCount(UserId $userId): int
    {
        return Notification::where('user_id', $userId->value())
            ->whereNull('read_at')
            ->notExpired()
            ->count();
    }

    /**
     * Clean up expired notifications
     */
    public function cleanupExpired(): int
    {
        return Notification::where('expires_at', '<=', now())->delete();
    }

    /**
     * Send notification through specific channel
     */
    private function sendThroughChannel(Notification $notification, string $channel): void
    {
        try {
            switch ($channel) {
                case 'email':
                    $this->sendEmail($notification);
                    break;
                case 'sms':
                    $this->sendSms($notification);
                    break;
                case 'push':
                    $this->sendPushNotification($notification);
                    break;
                case 'database':
                    // Already stored in database
                    break;
                default:
                    Log::warning("Unknown notification channel: {$channel}");
            }
        } catch (\Exception $e) {
            Log::error("Failed to send notification through {$channel}: " . $e->getMessage(), [
                'notification_id' => $notification->id,
                'channel' => $channel,
            ]);
        }
    }

    private function sendEmail(Notification $notification): void
    {
        $user = User::find($notification->user_id);
        if (!$user || !$user->email) {
            return;
        }

        // This would use your email templates
        Mail::send('emails.notification', [
            'notification' => $notification,
            'user' => $user,
        ], function ($message) use ($user, $notification) {
            $message->to($user->email)
                   ->subject($notification->title);
        });

        $notification->update(['sent_at' => now()]);
    }

    private function sendSms(Notification $notification): void
    {
        // Implement SMS sending logic (Twilio, etc.)
        // This is a placeholder
        Log::info("SMS notification sent", ['notification_id' => $notification->id]);
    }

    private function sendPushNotification(Notification $notification): void
    {
        // Implement push notification logic (FCM, etc.)
        // This is a placeholder
        Log::info("Push notification sent", ['notification_id' => $notification->id]);
    }
}
