<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Feedback extends Model
{
    use HasFactory;

    protected $table = 'feedback';

    protected $fillable = [
        'giver_id',
        'receiver_id',
        'auction_id',
        'rating',
        'comments',
        'feedback_type',
    ];

    protected function casts(): array
    {
        return [
            'rating' => 'integer',
        ];
    }

    /**
     * Get the user who gave the feedback.
     */
    public function giver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'giver_id');
    }

    /**
     * Get the user who received the feedback.
     */
    public function receiver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'receiver_id');
    }

    /**
     * Get the auction this feedback relates to.
     */
    public function auction(): BelongsTo
    {
        return $this->belongsTo(Auction::class);
    }

    /**
     * Scope to get feedback for a specific user as receiver.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('receiver_id', $userId);
    }

    /**
     * Scope to get feedback by a specific user as giver.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('giver_id', $userId);
    }

    /**
     * Scope to get feedback for a specific auction.
     */
    public function scopeForAuction($query, $auctionId)
    {
        return $query->where('auction_id', $auctionId);
    }

    /**
     * Scope to get feedback by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('feedback_type', $type);
    }

    /**
     * Get the average rating for a user.
     */
    public static function getAverageRatingForUser($userId): float
    {
        return static::where('receiver_id', $userId)->avg('rating') ?? 0.0;
    }

    /**
     * Get the total feedback count for a user.
     */
    public static function getFeedbackCountForUser($userId): int
    {
        return static::where('receiver_id', $userId)->count();
    }
}
