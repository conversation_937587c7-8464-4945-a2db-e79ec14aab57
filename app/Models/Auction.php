<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Auction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'category_id',
        'title',
        'description',
        'condition',
        'starting_price',
        'reserve_price',
        'current_bid',
        'bid_increment',
        'buyout_price',
        'start_time',
        'end_time',
        'actual_end_time',
        'status',
        'auto_extend',
        'extend_minutes',
        'shipping_cost',
        'shipping_options',
        'return_policy',
        'terms_conditions',
        'featured',
        'featured_until',
        'views_count',
        'watchers_count',
        'bids_count',
        'winner_id',
        'final_price',
        'commission_rate',
        'commission_amount',
        'payment_status',
        'payment_due_date',
        'shipping_status',
        'notes',
        'admin_notes',
    ];

    protected function casts(): array
    {
        return [
            'starting_price' => 'decimal:2',
            'reserve_price' => 'decimal:2',
            'current_bid' => 'decimal:2',
            'bid_increment' => 'decimal:2',
            'buyout_price' => 'decimal:2',
            'shipping_cost' => 'decimal:2',
            'final_price' => 'decimal:2',
            'commission_rate' => 'decimal:4',
            'commission_amount' => 'decimal:2',
            'start_time' => 'datetime',
            'end_time' => 'datetime',
            'actual_end_time' => 'datetime',
            'featured_until' => 'datetime',
            'payment_due_date' => 'date',
            'auto_extend' => 'boolean',
            'featured' => 'boolean',
            'extend_minutes' => 'integer',
            'views_count' => 'integer',
            'watchers_count' => 'integer',
            'bids_count' => 'integer',
            'shipping_options' => 'array',
        ];
    }

    /**
     * Get the seller of the auction.
     */
    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the winner of the auction.
     */
    public function winner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'winner_id');
    }

    /**
     * Get the category of the auction.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the bids for the auction.
     */
    public function bids(): HasMany
    {
        return $this->hasMany(Bid::class)->orderBy('amount', 'desc');
    }

    /**
     * Get the images for the auction.
     */
    public function images(): HasMany
    {
        return $this->hasMany(AuctionImage::class)->orderBy('sort_order');
    }

    /**
     * Get the primary image for the auction.
     */
    public function primaryImage()
    {
        return $this->hasOne(AuctionImage::class)->where('is_primary', true);
    }

    /**
     * Get the watchers of the auction.
     */
    public function watchers(): HasMany
    {
        return $this->hasMany(Watchlist::class);
    }

    /**
     * Get the payments for the auction.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the views for the auction.
     */
    public function views(): HasMany
    {
        return $this->hasMany(AuctionView::class);
    }

    /**
     * Scope to get active auctions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get featured auctions.
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true)
                    ->where(function ($q) {
                        $q->whereNull('featured_until')
                          ->orWhere('featured_until', '>', now());
                    });
    }

    /**
     * Scope to get ending soon auctions.
     */
    public function scopeEndingSoon($query, $hours = 24)
    {
        return $query->where('status', 'active')
                    ->where('end_time', '<=', now()->addHours($hours))
                    ->where('end_time', '>', now());
    }

    /**
     * Check if the auction is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' &&
               $this->start_time <= now() &&
               $this->end_time > now();
    }

    /**
     * Check if the auction has ended.
     */
    public function hasEnded(): bool
    {
        return $this->status === 'ended' || $this->end_time <= now();
    }

    /**
     * Check if the auction is featured.
     */
    public function isFeatured(): bool
    {
        return $this->featured &&
               ($this->featured_until === null || $this->featured_until > now());
    }

    /**
     * Check if the auction was won by a specific user.
     */
    public function isWonBy($userId): bool
    {
        if (is_object($userId) && method_exists($userId, 'value')) {
            $userId = $userId->value();
        }

        return $this->status === 'ended' && $this->winner_id == $userId;
    }

    /**
     * Get the time remaining in the auction.
     */
    public function getTimeRemainingAttribute(): ?\Carbon\Carbon
    {
        if ($this->hasEnded()) {
            return null;
        }

        return $this->end_time;
    }

    /**
     * Get the highest bid.
     */
    public function getHighestBidAttribute()
    {
        return $this->bids()->orderBy('amount', 'desc')->first();
    }

    /**
     * Get the is_featured attribute (accessor for frontend compatibility)
     */
    public function getIsFeaturedAttribute(): bool
    {
        return $this->isFeatured();
    }

    /**
     * Get the starting_bid attribute (accessor for frontend compatibility)
     */
    public function getStartingBidAttribute()
    {
        return $this->starting_price;
    }

    /**
     * Get the view_count attribute (accessor for frontend compatibility)
     */
    public function getViewCountAttribute(): int
    {
        return $this->views_count ?? 0;
    }

    /**
     * Get the user attribute (accessor for frontend compatibility)
     */
    public function getUserAttribute()
    {
        return $this->seller;
    }

    /**
     * Get the current_bid attribute (accessor for frontend compatibility)
     */
    public function getCurrentBidAttribute()
    {
        return $this->current_price ?? $this->starting_price;
    }

    /**
     * Get the buy_now_price attribute (accessor for frontend compatibility)
     */
    public function getBuyNowPriceAttribute()
    {
        return $this->buyout_price;
    }

    /**
     * Get the bids_count attribute (accessor for frontend compatibility)
     */
    public function getBidsCountAttribute(): int
    {
        return $this->bids()->count();
    }

    /**
     * Get the watchers_count attribute (accessor for frontend compatibility)
     */
    public function getWatchersCountAttribute(): int
    {
        return 0; // Placeholder - implement when watchers feature is added
    }
}
