<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use Inertia\Inertia;
use Inertia\Response;

class HomeController extends Controller
{
    public function index(): Response
    {
        // Mock featured auctions data
        $featuredAuctions = [
            [
                'id' => 1,
                'title' => 'Vintage Rolex Submariner 1960s',
                'description' => 'Rare vintage Rolex Submariner from the 1960s in excellent condition.',
                'condition' => 'good',
                'status' => 'active',
                'starting_price' => [
                    'amount' => 5000,
                    'currency' => 'USD',
                    'formatted' => '$5,000.00',
                ],
                'current_bid' => [
                    'amount' => 12500,
                    'currency' => 'USD',
                    'formatted' => '$12,500.00',
                ],
                'timing' => [
                    'start_time' => '2024-01-01T10:00:00Z',
                    'end_time' => now()->addDays(2)->toISOString(),
                    'duration_minutes' => 7200,
                    'remaining_minutes' => 2880,
                    'is_ending_soon' => false,
                ],
                'statistics' => [
                    'bids_count' => 23,
                    'views_count' => 456,
                    'watchers_count' => 34,
                ],
                'features' => [
                    'auto_extend' => true,
                    'extend_minutes' => 10,
                    'is_featured' => true,
                ],
                'seller' => [
                    'id' => 1,
                    'name' => '<PERSON>',
                    'email' => '<EMAIL>',
                    'role' => 'seller',
                    'avatar' => [
                        'url' => 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
                        'thumbnail' => 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face',
                    ],
                    'verification' => [
                        'is_email_verified' => true,
                        'is_phone_verified' => true,
                        'is_identity_verified' => true,
                        'verification_level' => 'fully_verified',
                    ],
                    'status' => [
                        'is_active' => true,
                        'is_online' => true,
                        'last_login_at' => now()->toISOString(),
                        'last_activity_at' => now()->toISOString(),
                    ],
                    'metadata' => [
                        'created_at' => '2023-01-15T10:00:00Z',
                        'updated_at' => now()->toISOString(),
                        'can_contact' => true,
                        'can_view_profile' => true,
                    ],
                ],
                'category' => [
                    'id' => 3,
                    'name' => 'Jewelry & Watches',
                    'description' => 'Fine jewelry, luxury watches, accessories',
                    'slug' => 'jewelry-watches',
                    'sort_order' => 3,
                    'is_active' => true,
                    'metadata' => [
                        'created_at' => '2023-01-01T00:00:00Z',
                        'updated_at' => '2023-01-01T00:00:00Z',
                    ],
                ],
                'images' => [
                    [
                        'id' => 1,
                        'filename' => 'rolex-1.jpg',
                        'url' => 'https://images.unsplash.com/photo-1523170335258-f5c6c6bd6eaf?w=800&h=600&fit=crop',
                        'thumbnail_url' => 'https://images.unsplash.com/photo-1523170335258-f5c6c6bd6eaf?w=300&h=225&fit=crop',
                        'alt_text' => 'Vintage Rolex Submariner',
                        'sort_order' => 0,
                        'is_primary' => true,
                        'metadata' => [
                            'original_name' => 'rolex-submariner.jpg',
                            'size_bytes' => 245760,
                            'mime_type' => 'image/jpeg',
                            'width' => 800,
                            'height' => 600,
                        ],
                    ],
                ],
                'metadata' => [
                    'created_at' => '2024-01-01T10:00:00Z',
                    'updated_at' => now()->toISOString(),
                    'slug' => 'vintage-rolex-submariner-1960s',
                    'is_watched' => false,
                    'user_has_bid' => false,
                    'can_bid' => true,
                    'can_edit' => false,
                ],
            ],
            [
                'id' => 2,
                'title' => 'MacBook Pro 16" M3 Max - Like New',
                'description' => 'Nearly new MacBook Pro 16" with M3 Max chip, 32GB RAM, 1TB SSD.',
                'condition' => 'like_new',
                'status' => 'active',
                'starting_price' => [
                    'amount' => 2000,
                    'currency' => 'USD',
                    'formatted' => '$2,000.00',
                ],
                'current_bid' => [
                    'amount' => 2850,
                    'currency' => 'USD',
                    'formatted' => '$2,850.00',
                ],
                'timing' => [
                    'start_time' => '2024-01-05T14:00:00Z',
                    'end_time' => now()->addHours(6)->toISOString(),
                    'duration_minutes' => 4320,
                    'remaining_minutes' => 360,
                    'is_ending_soon' => true,
                ],
                'statistics' => [
                    'bids_count' => 15,
                    'views_count' => 234,
                    'watchers_count' => 18,
                ],
                'features' => [
                    'auto_extend' => true,
                    'extend_minutes' => 5,
                    'is_featured' => true,
                ],
                'seller' => [
                    'id' => 2,
                    'name' => 'Sarah Johnson',
                    'email' => '<EMAIL>',
                    'role' => 'premium_seller',
                    'avatar' => [
                        'url' => 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
                        'thumbnail' => 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face',
                    ],
                    'verification' => [
                        'is_email_verified' => true,
                        'is_phone_verified' => true,
                        'is_identity_verified' => true,
                        'verification_level' => 'fully_verified',
                    ],
                    'status' => [
                        'is_active' => true,
                        'is_online' => false,
                        'last_login_at' => '2024-01-07T14:30:00Z',
                        'last_activity_at' => '2024-01-07T16:45:00Z',
                    ],
                    'metadata' => [
                        'created_at' => '2023-03-22T08:15:00Z',
                        'updated_at' => '2024-01-07T16:45:00Z',
                        'can_contact' => true,
                        'can_view_profile' => true,
                    ],
                ],
                'category' => [
                    'id' => 1,
                    'name' => 'Electronics',
                    'description' => 'Computers, phones, gadgets and more',
                    'slug' => 'electronics',
                    'sort_order' => 1,
                    'is_active' => true,
                    'metadata' => [
                        'created_at' => '2023-01-01T00:00:00Z',
                        'updated_at' => '2023-01-01T00:00:00Z',
                    ],
                ],
                'images' => [
                    [
                        'id' => 2,
                        'filename' => 'macbook-1.jpg',
                        'url' => 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=800&h=600&fit=crop',
                        'thumbnail_url' => 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=300&h=225&fit=crop',
                        'alt_text' => 'MacBook Pro M3 Max',
                        'sort_order' => 0,
                        'is_primary' => true,
                        'metadata' => [
                            'original_name' => 'macbook-pro.jpg',
                            'size_bytes' => 245760,
                            'mime_type' => 'image/jpeg',
                            'width' => 800,
                            'height' => 600,
                        ],
                    ],
                ],
                'metadata' => [
                    'created_at' => '2024-01-05T14:00:00Z',
                    'updated_at' => now()->toISOString(),
                    'slug' => 'macbook-pro-16-m3-max-like-new',
                    'is_watched' => true,
                    'user_has_bid' => false,
                    'can_bid' => true,
                    'can_edit' => false,
                ],
            ],
        ];

        // Mock ending soon auctions
        $endingSoonAuctions = [
            [
                'id' => 5,
                'title' => 'Vintage Gibson Les Paul 1959',
                'description' => 'Extremely rare 1959 Gibson Les Paul Standard in original sunburst finish.',
                'condition' => 'good',
                'status' => 'active',
                'starting_price' => [
                    'amount' => 100000,
                    'currency' => 'USD',
                    'formatted' => '$100,000.00',
                ],
                'current_bid' => [
                    'amount' => 185000,
                    'currency' => 'USD',
                    'formatted' => '$185,000.00',
                ],
                'timing' => [
                    'start_time' => '2024-01-02T12:00:00Z',
                    'end_time' => now()->addMinutes(45)->toISOString(),
                    'duration_minutes' => 10080,
                    'remaining_minutes' => 45,
                    'is_ending_soon' => true,
                ],
                'statistics' => [
                    'bids_count' => 42,
                    'views_count' => 1234,
                    'watchers_count' => 89,
                ],
                'features' => [
                    'auto_extend' => true,
                    'extend_minutes' => 15,
                    'is_featured' => true,
                ],
                'seller' => [
                    'id' => 1,
                    'name' => 'John Smith',
                    'email' => '<EMAIL>',
                    'role' => 'seller',
                    'avatar' => [
                        'url' => 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
                        'thumbnail' => 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face',
                    ],
                    'verification' => [
                        'is_email_verified' => true,
                        'is_phone_verified' => true,
                        'is_identity_verified' => true,
                        'verification_level' => 'fully_verified',
                    ],
                    'status' => [
                        'is_active' => true,
                        'is_online' => true,
                        'last_login_at' => now()->toISOString(),
                        'last_activity_at' => now()->toISOString(),
                    ],
                    'metadata' => [
                        'created_at' => '2023-01-15T10:00:00Z',
                        'updated_at' => now()->toISOString(),
                        'can_contact' => true,
                        'can_view_profile' => true,
                    ],
                ],
                'category' => [
                    'id' => 2,
                    'name' => 'Art & Collectibles',
                    'description' => 'Paintings, sculptures, rare collectibles',
                    'slug' => 'art-collectibles',
                    'sort_order' => 2,
                    'is_active' => true,
                    'metadata' => [
                        'created_at' => '2023-01-01T00:00:00Z',
                        'updated_at' => '2023-01-01T00:00:00Z',
                    ],
                ],
                'images' => [
                    [
                        'id' => 5,
                        'filename' => 'gibson-1.jpg',
                        'url' => 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=600&fit=crop',
                        'thumbnail_url' => 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=225&fit=crop',
                        'alt_text' => 'Vintage Gibson Les Paul',
                        'sort_order' => 0,
                        'is_primary' => true,
                        'metadata' => [
                            'original_name' => 'gibson-les-paul.jpg',
                            'size_bytes' => 245760,
                            'mime_type' => 'image/jpeg',
                            'width' => 800,
                            'height' => 600,
                        ],
                    ],
                ],
                'metadata' => [
                    'created_at' => '2024-01-02T12:00:00Z',
                    'updated_at' => now()->toISOString(),
                    'slug' => 'vintage-gibson-les-paul-1959',
                    'is_watched' => true,
                    'user_has_bid' => false,
                    'can_bid' => true,
                    'can_edit' => false,
                ],
            ],
        ];

        return Inertia::render('Home', [
            'featuredAuctions' => $featuredAuctions,
            'endingSoonAuctions' => $endingSoonAuctions,
        ]);
    }
}
