<?php

declare(strict_types=1);

namespace App\Http\Controllers\Web;

use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Auction\Repositories\BidRepositoryInterface;
use App\Domain\User\Repositories\UserRepositoryInterface;
use App\Domain\User\ValueObjects\UserId;
use App\Http\Controllers\Controller;
use App\Http\Resources\AuctionResource;
use App\Http\Resources\BidResource;
use App\Http\Resources\UserResource;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class UserController extends Controller
{
    public function __construct(
        private UserRepositoryInterface $userRepository,
        private AuctionRepositoryInterface $auctionRepository,
        private BidRepositoryInterface $bidRepository
    ) {}

    /**
     * Display user dashboard
     */
    public function dashboard(): Response
    {
        $userId = UserId::fromString((string) auth()->id());

        // Get user statistics
        $activeAuctions = $this->auctionRepository->countUserActiveAuctions($userId);
        $totalAuctions = $this->auctionRepository->countUserAuctions($userId);
        $activeBids = $this->bidRepository->countUserActiveBids($userId);
        $wonAuctions = $this->auctionRepository->countUserWonAuctions($userId);

        // Get recent activity
        $recentAuctions = $this->auctionRepository->findUserRecent($userId, 5);
        $recentBids = $this->bidRepository->findUserRecent($userId, 5);
        $watchedAuctions = $this->auctionRepository->findUserWatched($userId, 5);

        return Inertia::render('User/Dashboard', [
            'statistics' => [
                'active_auctions' => $activeAuctions,
                'total_auctions' => $totalAuctions,
                'active_bids' => $activeBids,
                'won_auctions' => $wonAuctions,
            ],
            'recent_auctions' => AuctionResource::collection($recentAuctions),
            'recent_bids' => BidResource::collection($recentBids),
            'watched_auctions' => AuctionResource::collection($watchedAuctions),
        ]);
    }

    /**
     * Display user's auctions
     */
    public function auctions(Request $request): Response
    {
        $perPage = min($request->get('per_page', 15), 50);
        $status = $request->get('status');
        $userId = UserId::fromString((string) auth()->id());

        if ($status) {
            $auctions = $this->auctionRepository->findUserByStatus($userId, $status, $perPage);
        } else {
            $auctions = $this->auctionRepository->findUserAuctions($userId, $perPage);
        }

        return Inertia::render('User/Auctions', [
            'auctions' => AuctionResource::collection($auctions),
            'filters' => [
                'status' => $status,
                'per_page' => $perPage,
            ],
        ]);
    }

    /**
     * Display user's bids
     */
    public function bids(Request $request): Response
    {
        $perPage = min($request->get('per_page', 15), 50);
        $status = $request->get('status');
        $userId = UserId::fromString((string) auth()->id());

        if ($status === 'winning') {
            $bids = $this->bidRepository->findUserWinning($userId, $perPage);
        } elseif ($status === 'outbid') {
            $bids = $this->bidRepository->findUserOutbid($userId, $perPage);
        } else {
            $bids = $this->bidRepository->findUserBids($userId, $perPage);
        }

        return Inertia::render('User/Bids', [
            'bids' => BidResource::collection($bids),
            'filters' => [
                'status' => $status,
                'per_page' => $perPage,
            ],
        ]);
    }

    /**
     * Display user's watched auctions
     */
    public function watchlist(Request $request): Response
    {
        $perPage = min($request->get('per_page', 15), 50);
        $userId = UserId::fromString((string) auth()->id());

        $auctions = $this->auctionRepository->findUserWatched($userId, $perPage);

        return Inertia::render('User/Watchlist', [
            'auctions' => AuctionResource::collection($auctions),
        ]);
    }

    /**
     * Display user's won auctions
     */
    public function wonAuctions(Request $request): Response
    {
        $perPage = min($request->get('per_page', 15), 50);
        $userId = UserId::fromString((string) auth()->id());

        $auctions = $this->auctionRepository->findUserWon($userId, $perPage);

        return Inertia::render('User/WonAuctions', [
            'auctions' => AuctionResource::collection($auctions),
        ]);
    }

    /**
     * Display user profile
     */
    public function profile(): Response
    {
        $user = auth()->user();

        return Inertia::render('User/Profile', [
            'user' => new UserResource($user),
        ]);
    }
}
