<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'role' => $this->role,
            'avatar' => [
                'url' => null,
                'thumbnail' => null,
            ],
        ];
    }

    /**
     * Determine if email should be shown
     */
    private function shouldShowEmail(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        // Show email if:
        // 1. Current user viewing their own profile
        // 2. Admin viewing any profile
        return $this->id === auth()->id() || auth()->user()->isAdmin();
    }

    /**
     * Determine if phone should be shown
     */
    private function shouldShowPhone(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        // Show phone if:
        // 1. Current user viewing their own profile
        // 2. Admin viewing any profile
        // 3. User has made phone public in privacy settings
        return $this->id === auth()->id() ||
               auth()->user()->isAdmin() ||
               ($this->privacy_settings['phone_public'] ?? false);
    }

    /**
     * Determine if statistics should be shown
     */
    private function shouldShowStatistics(): bool
    {
        // Show statistics if:
        // 1. Current user viewing their own profile
        // 2. Profile is public
        // 3. Admin viewing any profile
        if (!auth()->check()) {
            return $this->privacy_settings['profile_public'] ?? true;
        }

        return $this->id === auth()->id() ||
               auth()->user()->isAdmin() ||
               ($this->privacy_settings['statistics_public'] ?? true);
    }

    /**
     * Determine if preferences should be shown
     */
    private function shouldShowPreferences(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        // Only show preferences to the user themselves or admin
        return $this->id === auth()->id() || auth()->user()->isAdmin();
    }

    /**
     * Get verification level
     */
    private function getVerificationLevel(): string
    {
        $level = 'unverified';

        if ($this->email_verified_at) {
            $level = 'email_verified';
        }

        if ($this->profile?->phone_verified_at) {
            $level = 'phone_verified';
        }

        if ($this->profile?->identity_verified_at) {
            $level = 'fully_verified';
        }

        return $level;
    }

    /**
     * Check if user is online
     */
    private function isOnline(): bool
    {
        if (!$this->last_activity_at) {
            return false;
        }

        return $this->last_activity_at->diffInMinutes(now()) <= 5;
    }

    /**
     * Calculate bid success rate
     */
    private function calculateBidSuccessRate(): float
    {
        $totalBids = $this->bids()->count();
        if ($totalBids === 0) {
            return 0.0;
        }

        $winningBids = $this->bids()->where('is_winning', true)->count();
        return round(($winningBids / $totalBids) * 100, 2);
    }

    /**
     * Calculate total spent
     */
    private function calculateTotalSpent(): float
    {
        return $this->payments()
            ->where('status', 'succeeded')
            ->sum('amount') ?? 0.0;
    }

    /**
     * Calculate total earned
     */
    private function calculateTotalEarned(): float
    {
        return $this->auctions()
            ->where('status', 'ended')
            ->whereNotNull('final_price')
            ->sum('final_price') ?? 0.0;
    }

    /**
     * Check if user can be contacted
     */
    private function canBeContactedBy($user): bool
    {
        if (!$user) {
            return false;
        }

        if ($user->isAdmin()) {
            return true;
        }

        return $this->privacy_settings['allow_contact'] ?? true;
    }

    /**
     * Check if profile can be viewed
     */
    private function canViewProfile($user): bool
    {
        if ($this->privacy_settings['profile_public'] ?? true) {
            return true;
        }

        if (!$user) {
            return false;
        }

        return $user->id === $this->id || $user->isAdmin();
    }
}
