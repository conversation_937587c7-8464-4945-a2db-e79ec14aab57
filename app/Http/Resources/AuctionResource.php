<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AuctionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'condition' => $this->condition,
            'status' => $this->status,
            'starting_price' => [
                'amount' => $this->starting_price,
                'currency' => 'USD',
                'formatted' => '$' . number_format((float) $this->starting_price, 2),
            ],
            'current_bid' => [
                'amount' => $this->current_bid,
                'currency' => 'USD',
                'formatted' => '$' . number_format((float) ($this->current_bid ?? 0), 2),
            ],
            'reserve_price' => $this->when($this->reserve_price, [
                'amount' => $this->reserve_price,
                'currency' => 'USD',
                'formatted' => '$' . number_format((float) ($this->reserve_price ?? 0), 2),
                'is_met' => (float) $this->current_bid >= (float) $this->reserve_price,
            ]),
            'buyout_price' => $this->when($this->buyout_price, [
                'amount' => $this->buyout_price,
                'currency' => 'USD',
                'formatted' => '$' . number_format((float) ($this->buyout_price ?? 0), 2),
            ]),
            'final_price' => $this->when($this->final_price, [
                'amount' => $this->final_price,
                'currency' => 'USD',
                'formatted' => '$' . number_format((float) ($this->final_price ?? 0), 2),
            ]),
            'shipping_cost' => $this->when($this->shipping_cost, [
                'amount' => $this->shipping_cost,
                'currency' => 'USD',
                'formatted' => '$' . number_format((float) ($this->shipping_cost ?? 0), 2),
            ]),
            'timing' => [
                'start_time' => $this->start_time?->toISOString(),
                'end_time' => $this->end_time?->toISOString(),
                'actual_end_time' => $this->actual_end_time?->toISOString(),
                'duration_minutes' => $this->start_time && $this->end_time ?
                    $this->start_time->diffInMinutes($this->end_time) : null,
                'remaining_minutes' => $this->status === 'active' && $this->end_time ?
                    max(0, now()->diffInMinutes($this->end_time, false)) : null,
                'is_ending_soon' => $this->status === 'active' && $this->end_time &&
                    now()->diffInHours($this->end_time) <= 24,
            ],
            'statistics' => [
                'bids_count' => $this->bids_count ?? 0,
                'views_count' => $this->views_count ?? 0,
                'watchers_count' => $this->watchers_count ?? 0,
            ],
            'features' => [
                'auto_extend' => $this->auto_extend,
                'extend_minutes' => $this->extend_minutes,
                'is_featured' => $this->featured,
            ],
            'seller' => $this->seller ? new UserResource($this->seller) : null,
            'category' => new CategoryResource($this->whenLoaded('category')),
            'images' => $this->whenLoaded('images', function () {
                return $this->images->map(function ($image) {
                    return [
                        'id' => $image->id,
                        'url' => $image->url,
                        'thumbnail_url' => $image->thumbnail_url,
                        'alt_text' => $image->alt_text,
                        'is_primary' => $image->is_primary,
                    ];
                });
            }),
            'highest_bid' => new BidResource($this->whenLoaded('highestBid')),
            'winner' => new UserResource($this->whenLoaded('winner')),
            'metadata' => [
                'created_at' => $this->created_at->toISOString(),
                'updated_at' => $this->updated_at->toISOString(),
                'is_watched' => $this->when(
                    auth()->check(),
                    fn() => $this->isWatchedByUser(auth()->id())
                ),
                'user_has_bid' => $this->when(
                    auth()->check(),
                    fn() => $this->bids()->where('user_id', auth()->id())->exists()
                ),
                'can_bid' => $this->when(
                    auth()->check(),
                    fn() => $this->status === 'active' &&
                           $this->user_id !== auth()->id() &&
                           $this->end_time > now()
                ),
                'can_edit' => $this->when(
                    auth()->check(),
                    fn() => $this->user_id === auth()->id() &&
                           in_array($this->status, ['draft', 'scheduled'])
                ),
            ],
        ];
    }
}
