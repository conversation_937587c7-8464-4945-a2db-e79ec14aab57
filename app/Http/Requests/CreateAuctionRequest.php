<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateAuctionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->canCreateAuctions();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'category_id' => 'required|integer|exists:categories,id',
            'title' => 'required|string|max:500',
            'description' => 'required|string|max:10000',
            'condition' => 'required|string|in:new,like_new,good,fair,poor',
            'starting_price' => 'required|numeric|min:0.01|max:1000000',
            'reserve_price' => 'nullable|numeric|min:0.01|max:1000000|gte:starting_price',
            'buyout_price' => 'nullable|numeric|min:0.01|max:1000000|gt:starting_price',
            'currency' => 'nullable|string|in:USD,EUR,GBP,CAD',
            'start_time' => 'nullable|date|after_or_equal:now',
            'end_time' => 'required|date|after:start_time',
            'shipping_cost' => 'nullable|numeric|min:0|max:1000',
            'shipping_options' => 'nullable|array',
            'shipping_options.*' => 'string|max:255',
            'return_policy' => 'nullable|string|max:1000',
            'terms_conditions' => 'nullable|string|max:2000',
            'auto_extend' => 'nullable|boolean',
            'extend_minutes' => 'nullable|integer|min:1|max:60',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'category_id.required' => 'Please select a category for your auction.',
            'category_id.exists' => 'The selected category is invalid.',
            'title.required' => 'Please provide a title for your auction.',
            'title.max' => 'The title cannot exceed 500 characters.',
            'description.required' => 'Please provide a description for your auction.',
            'description.max' => 'The description cannot exceed 10,000 characters.',
            'starting_price.required' => 'Please set a starting price for your auction.',
            'starting_price.min' => 'The starting price must be at least $0.01.',
            'starting_price.max' => 'The starting price cannot exceed $1,000,000.',
            'reserve_price.gte' => 'The reserve price must be greater than or equal to the starting price.',
            'buyout_price.gt' => 'The buyout price must be greater than the starting price.',
            'end_time.required' => 'Please set an end time for your auction.',
            'end_time.after' => 'The end time must be after the start time.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate auction duration
            if ($this->has(['start_time', 'end_time'])) {
                $startTime = $this->start_time ? strtotime($this->start_time) : time();
                $endTime = strtotime($this->end_time);
                $durationMinutes = ($endTime - $startTime) / 60;

                $minDuration = config('auction.auction.min_duration_minutes', 30);
                $maxDuration = config('auction.auction.max_duration_days', 30) * 24 * 60;

                if ($durationMinutes < $minDuration) {
                    $validator->errors()->add('end_time', "Auction duration must be at least {$minDuration} minutes.");
                }

                if ($durationMinutes > $maxDuration) {
                    $maxDays = config('auction.auction.max_duration_days', 30);
                    $validator->errors()->add('end_time', "Auction duration cannot exceed {$maxDays} days.");
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'user_id' => auth()->id(),
        ]);
    }
}
