<?php

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Comprehensive Null Safety Tests', function () {
    beforeEach(function () {
        $this->user = User::factory()->create([
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
        $this->category = Category::factory()->create();
        $this->auction = Auction::factory()->create([
            'category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'status' => 'active',
        ]);
    });

    describe('Public Pages (No Auth Required)', function () {
        test('home page handles missing data gracefully', function () {
            $response = $this->get('/');
            $response->assertStatus(200);

            $props = $response->json('props');
            expect($props)->toHaveKey('featured_auctions');

            // Check that featured auctions have required structure
            if (isset($props['featured_auctions']) && !empty($props['featured_auctions'])) {
                foreach ($props['featured_auctions'] as $auction) {
                    expect($auction)->toHaveKey('status');
                    expect($auction)->toHaveKey('features');
                    expect($auction)->toHaveKey('timing');
                    expect($auction)->toHaveKey('statistics');
                }
            }
        });

        test('auction listing page handles missing data gracefully', function () {
            $response = $this->get('/auctions');
            $response->assertStatus(200);

            $props = $response->json('props');
            expect($props)->toHaveKey('auctions');
            expect($props['auctions'])->toHaveKey('data');

            foreach ($props['auctions']['data'] as $auction) {
                // Required fields that should never be null
                expect($auction)->toHaveKey('id');
                expect($auction)->toHaveKey('title');
                expect($auction)->toHaveKey('status');
                expect($auction['status'])->not->toBeNull();

                // Optional fields that should have keys even if null
                expect($auction)->toHaveKey('features');
                expect($auction)->toHaveKey('timing');
                expect($auction)->toHaveKey('statistics');
                expect($auction)->toHaveKey('metadata');
                expect($auction)->toHaveKey('current_bid');
                expect($auction)->toHaveKey('images');
            }
        });

        test('auction detail page handles missing data gracefully', function () {
            $response = $this->get("/auctions/{$this->auction->id}");
            $response->assertStatus(200);

            $props = $response->json('props');
            expect($props)->toHaveKey('auction');

            $auction = $props['auction'];

            // Critical fields that should never be null
            expect($auction['status'])->not->toBeNull();
            expect($auction['title'])->not->toBeNull();
            expect($auction['seller'])->not->toBeNull();
            expect($auction['category'])->not->toBeNull();

            // Optional fields that should have structure
            expect($auction)->toHaveKey('features');
            expect($auction)->toHaveKey('timing');
            expect($auction)->toHaveKey('statistics');
            expect($auction)->toHaveKey('metadata');
            expect($auction)->toHaveKey('current_bid');
            expect($auction)->toHaveKey('reserve_price');
            expect($auction)->toHaveKey('buyout_price');
            expect($auction)->toHaveKey('shipping_cost');
        });

        test('category pages handle missing data gracefully', function () {
            $response = $this->get("/categories/{$this->category->slug}");
            $response->assertStatus(200);

            $props = $response->json('props');
            expect($props)->toHaveKey('category');
            expect($props)->toHaveKey('auctions');

            $category = $props['category'];
            expect($category['name'])->not->toBeNull();
            expect($category['slug'])->not->toBeNull();
        });
    });

    describe('Authenticated Pages', function () {
        test('dashboard handles missing data gracefully', function () {
            $response = $this->actingAs($this->user)->get('/dashboard');
            $response->assertStatus(200);

            $props = $response->json('props');
            expect($props['auth']['user'])->not->toBeNull();
            expect($props['auth']['user'])->toHaveKey('avatar');
        });

        test('user profile handles missing data gracefully', function () {
            $response = $this->actingAs($this->user)->get('/my/profile');
            $response->assertStatus(200);

            $props = $response->json('props');
            expect($props)->toHaveKey('user');
            expect($props['user'])->not->toBeNull();
        });

        test('user auctions page handles missing data gracefully', function () {
            $response = $this->actingAs($this->user)->get('/my/auctions');
            $response->assertStatus(200);

            $props = $response->json('props');
            expect($props)->toHaveKey('auctions');
            expect($props['auctions'])->toHaveKey('data');
        });

        test('watchlist page handles missing data gracefully', function () {
            $response = $this->actingAs($this->user)->get('/watchlist');
            $response->assertStatus(200);

            $props = $response->json('props');
            expect($props)->toHaveKey('auctions');
            expect($props['auctions'])->toHaveKey('data');
        });
    });

    describe('Authentication Requirements', function () {
        test('protected pages redirect unauthenticated users', function () {
            $protectedRoutes = [
                '/dashboard',
                '/my/profile',
                '/my/auctions',
                '/watchlist',
                '/auctions/create',
            ];

            foreach ($protectedRoutes as $route) {
                $response = $this->get($route);
                expect($response->status())->toBeIn([302, 401, 403]);
            }
        });

        test('auction creation requires authentication', function () {
            $response = $this->get('/auctions/create');
            expect($response->status())->toBeIn([302, 401, 403]);
        });
    });

    describe('Data Structure Validation', function () {
        test('auction resources always have required metadata structure', function () {
            $response = $this->get('/api/auctions');

            if ($response->status() === 200) {
                $data = $response->json('data.data');

                foreach ($data as $auction) {
                    expect($auction)->toHaveKey('metadata');
                    expect($auction['metadata'])->toBeArray();
                    expect($auction['metadata'])->toHaveKey('created_at');
                    expect($auction['metadata'])->toHaveKey('updated_at');
                }
            }
        });

        test('user resources always have required structure', function () {
            $response = $this->actingAs($this->user)->get('/api/user');

            if ($response->status() === 200) {
                $user = $response->json('data');
                expect($user)->toHaveKey('id');
                expect($user)->toHaveKey('name');
                expect($user)->toHaveKey('email');
                expect($user)->toHaveKey('avatar');
            }
        });
    });

    describe('Error Handling', function () {
        test('malformed auction IDs return 404 not 500', function () {
            $malformedIds = ['abc', 'null', 'undefined', '12.5', '-1'];

            foreach ($malformedIds as $id) {
                $response = $this->get("/auctions/{$id}");
                expect($response->status())->toBe(404);
            }
        });

        test('non-existent auction IDs return 404', function () {
            $response = $this->get('/auctions/999999');
            expect($response->status())->toBe(404);
        });
    });
});
