<?php

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Simple Debug Tests', function () {
    beforeEach(function () {
        $this->user = User::factory()->create([
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
        $this->category = Category::factory()->create();
        $this->auction = Auction::factory()->create([
            'category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'status' => 'active',
        ]);
    });

    test('check auction detail response type', function () {
        $response = $this->get("/auctions/{$this->auction->id}");
        $response->assertStatus(200);
        
        $content = $response->getContent();
        $contentType = $response->headers->get('content-type');
        
        dump('Content Type:', $contentType);
        dump('Content Length:', strlen($content));
        dump('First 200 chars:', substr($content, 0, 200));
        
        // Check if it's HTML or JSON
        $isJson = json_decode($content) !== null;
        dump('Is JSON:', $isJson);
        
        expect(true)->toBeTrue(); // Just to pass the test
    });

    test('check home page response type', function () {
        $response = $this->get('/');
        $response->assertStatus(200);
        
        $content = $response->getContent();
        $contentType = $response->headers->get('content-type');
        
        dump('Content Type:', $contentType);
        dump('Content Length:', strlen($content));
        dump('First 200 chars:', substr($content, 0, 200));
        
        // Check if it's HTML or JSON
        $isJson = json_decode($content) !== null;
        dump('Is JSON:', $isJson);
        
        expect(true)->toBeTrue(); // Just to pass the test
    });
});
