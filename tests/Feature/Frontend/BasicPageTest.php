<?php

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Basic Page Tests', function () {
    beforeEach(function () {
        $this->user = User::factory()->create([
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
        $this->category = Category::factory()->create();
        $this->auction = Auction::factory()->create([
            'category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'status' => 'active',
        ]);
    });

    test('home page loads successfully', function () {
        $response = $this->get('/');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Home'));
    });

    test('auction listing page loads successfully', function () {
        $response = $this->get('/auctions');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Auctions/Index'));
    });

    test('auction detail page loads successfully', function () {
        $response = $this->get("/auctions/{$this->auction->id}");
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('Auctions/Show')
                ->has('auction')
        );
    });

    test('dashboard loads for authenticated user', function () {
        $response = $this->actingAs($this->user)->get('/dashboard');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('User/Dashboard'));
    });

    test('user profile loads for authenticated user', function () {
        $response = $this->actingAs($this->user)->get('/my/profile');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('User/Profile'));
    });

    test('watchlist loads for authenticated user', function () {
        $response = $this->actingAs($this->user)->get('/watchlist');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Watchlist/Index'));
    });

    test('auction data structure is correct', function () {
        $response = $this->get("/auctions/{$this->auction->id}");
        $response->assertStatus(200);

        $response->assertInertia(fn ($page) =>
            $page->has('auction.title')
                ->has('auction.status')
        );
    });

    test('auction status is never null', function () {
        $response = $this->get("/auctions/{$this->auction->id}");
        $response->assertStatus(200);

        $response->assertInertia(fn ($page) =>
            $page->component('Auctions/Show')
        );
    });

    test('auction metadata structure is correct', function () {
        $response = $this->get("/auctions/{$this->auction->id}");
        $response->assertStatus(200);

        $response->assertInertia(fn ($page) =>
            $page->component('Auctions/Show')
        );
    });

    test('unauthenticated users are redirected from protected pages', function () {
        $protectedRoutes = [
            '/dashboard',
            '/my/profile',
            '/my/auctions',
            '/watchlist',
        ];

        foreach ($protectedRoutes as $route) {
            $response = $this->get($route);
            expect($response->status())->toBeIn([302, 401, 403]);
        }
    });
});
