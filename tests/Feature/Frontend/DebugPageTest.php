<?php

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Debug Page Tests', function () {
    beforeEach(function () {
        $this->user = User::factory()->create([
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
        $this->category = Category::factory()->create();
        $this->auction = Auction::factory()->create([
            'category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'status' => 'active',
        ]);
    });

    test('debug auction detail page data structure', function () {
        $response = $this->get("/auctions/{$this->auction->id}");
        $response->assertStatus(200);
        
        $response->assertInertia(fn ($page) => 
            $page->component('Auctions/Show')
                ->has('auction')
        );
        
        // Let's see what the actual structure is
        $props = $response->json('props');
        dump('Auction props:', $props['auction'] ?? 'NO AUCTION PROP');
        
        // Check if it's nested differently
        if (isset($props['auction'])) {
            dump('Auction keys:', array_keys($props['auction']));
        }
        
        expect(true)->toBeTrue(); // Just to pass the test
    });

    test('debug home page data structure', function () {
        $response = $this->get('/');
        $response->assertStatus(200);
        
        $props = $response->json('props');
        dump('Home props keys:', array_keys($props));
        
        expect(true)->toBeTrue(); // Just to pass the test
    });
});
