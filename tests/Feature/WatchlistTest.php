<?php

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;
use App\Models\Watchlist;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Watchlist Functionality', function () {
    beforeEach(function () {
        $this->user = User::factory()->create();
        $this->category = Category::factory()->create();
        $this->auction = Auction::factory()->create([
            'category_id' => $this->category->id,
            'status' => 'active',
        ]);
    });

    test('user can add auction to watchlist', function () {
        $this->actingAs($this->user);

        $response = $this->post('/watchlist', [
            'auction_id' => $this->auction->id,
        ]);

        $response->assertStatus(200);
        
        expect(Watchlist::where('user_id', $this->user->id)
            ->where('auction_id', $this->auction->id)
            ->exists())->toBeTrue();
    });

    test('user can remove auction from watchlist', function () {
        $this->actingAs($this->user);

        // First add to watchlist
        Watchlist::create([
            'user_id' => $this->user->id,
            'auction_id' => $this->auction->id,
        ]);

        $response = $this->delete("/watchlist/{$this->auction->id}");

        $response->assertStatus(200);
        
        expect(Watchlist::where('user_id', $this->user->id)
            ->where('auction_id', $this->auction->id)
            ->exists())->toBeFalse();
    });

    test('auction resource includes is_watched field when user is authenticated', function () {
        $this->actingAs($this->user);

        // Add auction to watchlist
        Watchlist::create([
            'user_id' => $this->user->id,
            'auction_id' => $this->auction->id,
        ]);

        $response = $this->get('/api/auctions');

        $response->assertStatus(200);
        
        $auctionData = $response->json('data.data');
        $watchedAuction = collect($auctionData)->firstWhere('id', $this->auction->id);
        
        expect($watchedAuction)->not->toBeNull();
        expect($watchedAuction['metadata']['is_watched'])->toBeTrue();
    });

    test('auction resource shows is_watched as false when not in watchlist', function () {
        $this->actingAs($this->user);

        $response = $this->get('/api/auctions');

        $response->assertStatus(200);
        
        $auctionData = $response->json('data.data');
        $auction = collect($auctionData)->firstWhere('id', $this->auction->id);
        
        expect($auction)->not->toBeNull();
        expect($auction['metadata']['is_watched'])->toBeFalse();
    });

    test('auction resource does not include is_watched when user is not authenticated', function () {
        $response = $this->get('/api/auctions');

        $response->assertStatus(200);
        
        $auctionData = $response->json('data.data');
        $auction = collect($auctionData)->firstWhere('id', $this->auction->id);
        
        expect($auction)->not->toBeNull();
        expect($auction['metadata'])->not->toHaveKey('is_watched');
    });

    test('watchlist check endpoint returns correct status', function () {
        $this->actingAs($this->user);

        // Test when not watched
        $response = $this->get("/watchlist/check/{$this->auction->id}");
        $response->assertStatus(200);
        expect($response->json('data.is_watched'))->toBeFalse();

        // Add to watchlist
        Watchlist::create([
            'user_id' => $this->user->id,
            'auction_id' => $this->auction->id,
        ]);

        // Test when watched
        $response = $this->get("/watchlist/check/{$this->auction->id}");
        $response->assertStatus(200);
        expect($response->json('data.is_watched'))->toBeTrue();
    });

    test('watchlist toggle endpoint works correctly', function () {
        $this->actingAs($this->user);

        // Toggle to add
        $response = $this->post("/watchlist/toggle/{$this->auction->id}");
        $response->assertStatus(200);
        expect($response->json('data.is_watched'))->toBeTrue();
        
        expect(Watchlist::where('user_id', $this->user->id)
            ->where('auction_id', $this->auction->id)
            ->exists())->toBeTrue();

        // Toggle to remove
        $response = $this->post("/watchlist/toggle/{$this->auction->id}");
        $response->assertStatus(200);
        expect($response->json('data.is_watched'))->toBeFalse();
        
        expect(Watchlist::where('user_id', $this->user->id)
            ->where('auction_id', $this->auction->id)
            ->exists())->toBeFalse();
    });

    test('user watchlist page shows watched auctions', function () {
        $this->actingAs($this->user);

        // Add auction to watchlist
        Watchlist::create([
            'user_id' => $this->user->id,
            'auction_id' => $this->auction->id,
        ]);

        $response = $this->get('/user/watchlist');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('auctions.data')
                ->where('auctions.data.0.id', $this->auction->id)
        );
    });

    test('watchedAuctions relationship works correctly', function () {
        // Add auction to watchlist
        Watchlist::create([
            'user_id' => $this->user->id,
            'auction_id' => $this->auction->id,
        ]);

        $watchedAuctions = $this->user->watchedAuctions;

        expect($watchedAuctions)->toHaveCount(1);
        expect($watchedAuctions->first()->id)->toBe($this->auction->id);
    });

    test('watchingUsers relationship works correctly', function () {
        // Add auction to watchlist
        Watchlist::create([
            'user_id' => $this->user->id,
            'auction_id' => $this->auction->id,
        ]);

        $watchingUsers = $this->auction->watchingUsers;

        expect($watchingUsers)->toHaveCount(1);
        expect($watchingUsers->first()->id)->toBe($this->user->id);
    });

    test('isWatchedByUser method works correctly', function () {
        expect($this->auction->isWatchedByUser($this->user->id))->toBeFalse();

        // Add to watchlist
        Watchlist::create([
            'user_id' => $this->user->id,
            'auction_id' => $this->auction->id,
        ]);

        expect($this->auction->isWatchedByUser($this->user->id))->toBeTrue();
        expect($this->auction->isWatchedByUser(null))->toBeFalse();
        expect($this->auction->isWatchedByUser(999))->toBeFalse();
    });
});
